import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { FormHandlerService } from './form-handler.service';
import { ToastService } from './toast.service';
import { ErrorHandlerService } from './error-handler.service';
import { DialogHandlerConfig } from '@shared/components/edit-create-dialog';
import { PopoverHandlerConfig } from '@shared/components/generic-popover';

// Internal interface for testing
interface FormOperationContext {
  config: DialogHandlerConfig | PopoverHandlerConfig;
  formValue: any;
  editMode?: boolean;
}

describe('FormHandlerService', () => {
  let service: FormHandlerService;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockErrorHandlerService: jasmine.SpyObj<ErrorHandlerService>;
  let mockApiService: jasmine.SpyObj<any>;

  beforeEach(() => {
    const toastSpy = jasmine.createSpyObj('ToastService', ['showSuccess']);
    const errorSpy = jasmine.createSpyObj('ErrorHandlerService', ['handleInternal']);
    const apiSpy = jasmine.createSpyObj('ApiService', ['createEmployee', 'updateEmployee', 'retireEmployee']);

    TestBed.configureTestingModule({
      providers: [
        FormHandlerService,
        { provide: ToastService, useValue: toastSpy },
        { provide: ErrorHandlerService, useValue: errorSpy }
      ]
    });

    service = TestBed.inject(FormHandlerService);
    mockToastService = TestBed.inject(ToastService) as jasmine.SpyObj<ToastService>;
    mockErrorHandlerService = TestBed.inject(ErrorHandlerService) as jasmine.SpyObj<ErrorHandlerService>;
    mockApiService = apiSpy;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Dialog Operations (Create/Update)', () => {
    let dialogConfig: DialogHandlerConfig;
    let onSuccessSpy: jasmine.Spy;

    beforeEach(() => {
      onSuccessSpy = jasmine.createSpy('onSuccess');
      dialogConfig = {
        service: mockApiService,
        createMethod: 'createEmployee',
        updateMethod: 'updateEmployee',
        entityLabel: 'nhân viên',
        createDataTransform: (formValue) => ({ ...formValue, code: formValue.code?.toUpperCase() }),
        updateDataTransform: (formValue) => ({ id: formValue.id, ...formValue }),
        onSuccess: onSuccessSpy
      };
    });

    it('should handle create operation successfully', async () => {
      // Arrange
      const formValue = { code: 'emp001', name: 'John Doe' };
      const transformedData = { code: 'EMP001', name: 'John Doe' };
      const context: FormOperationContext = {
        config: dialogConfig,
        formValue,
        editMode: false
      };

      mockApiService.createEmployee.and.returnValue(of({ success: true }));
      mockErrorHandlerService.handleInternal.and.returnValue({ hasError: false, data: null });

      // Act
      const result = await service.handleSave(context);

      // Assert
      expect(mockApiService.createEmployee).toHaveBeenCalledWith(transformedData);
      expect(mockToastService.showSuccess).toHaveBeenCalledWith('Tạo nhân viên thành công');
      expect(onSuccessSpy).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('should handle update operation successfully', async () => {
      // Arrange
      const formValue = { id: 1, code: 'emp001', name: 'John Doe Updated' };
      const context: FormOperationContext = {
        config: dialogConfig,
        formValue,
        editMode: true
      };

      mockApiService.updateEmployee.and.returnValue(of({ success: true }));
      mockErrorHandlerService.handleInternal.and.returnValue({ hasError: false, data: null });

      // Act
      const result = await service.handleSave(context);

      // Assert
      expect(mockApiService.updateEmployee).toHaveBeenCalledWith(formValue);
      expect(mockToastService.showSuccess).toHaveBeenCalledWith('Cập nhật nhân viên thành công');
      expect(onSuccessSpy).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });
  });

  describe('Popover Operations (Single Action)', () => {
    let popoverConfig: PopoverHandlerConfig;
    let onSuccessSpy: jasmine.Spy;

    beforeEach(() => {
      onSuccessSpy = jasmine.createSpy('onSuccess');
      popoverConfig = {
        service: mockApiService,
        method: 'retireEmployee',
        entityLabel: 'nhân viên',
        dataTransform: (formValue) => ({ employeeId: formValue.id, reason: formValue.reason }),
        onSuccess: onSuccessSpy
      };
    });

    it('should handle popover operation successfully', async () => {
      // Arrange
      const formValue = { id: 1, reason: 'Retirement' };
      const transformedData = { employeeId: 1, reason: 'Retirement' };
      const context: FormOperationContext = {
        config: popoverConfig,
        formValue
      };

      mockApiService.retireEmployee.and.returnValue(of({ success: true }));
      mockErrorHandlerService.handleInternal.and.returnValue({ hasError: false, data: null });

      // Act
      const result = await service.handleSave(context);

      // Assert
      expect(mockApiService.retireEmployee).toHaveBeenCalledWith(transformedData);
      expect(mockToastService.showSuccess).toHaveBeenCalledWith('Thao tác nhân viên thành công');
      expect(onSuccessSpy).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('should handle missing service method error', async () => {
      // Arrange
      const invalidConfig: PopoverHandlerConfig = {
        ...popoverConfig,
        method: 'nonExistentMethod'
      };
      const context: FormOperationContext = {
        config: invalidConfig,
        formValue: { id: 1 }
      };

      // Act
      const result = await service.handleSave(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Method nonExistentMethod not found on service');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Arrange
      const config: PopoverHandlerConfig = {
        service: mockApiService,
        method: 'retireEmployee',
        entityLabel: 'nhân viên'
      };
      const context: FormOperationContext = {
        config,
        formValue: { id: 1 }
      };

      mockApiService.retireEmployee.and.returnValue(throwError(() => 'API Error'));

      // Act
      const result = await service.handleSave(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('API Error');
    });

    it('should handle internal errors from error handler service', async () => {
      // Arrange
      const config: PopoverHandlerConfig = {
        service: mockApiService,
        method: 'retireEmployee',
        entityLabel: 'nhân viên'
      };
      const context: FormOperationContext = {
        config,
        formValue: { id: 1 }
      };

      mockApiService.retireEmployee.and.returnValue(of({ error: 'Internal error' }));
      mockErrorHandlerService.handleInternal.and.returnValue({ hasError: true, data: null });

      // Act
      const result = await service.handleSave(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toEqual({ error: 'Internal error' });
    });
  });

  describe('Cancel Handling', () => {
    it('should call onCancel callback when provided', () => {
      // Arrange
      const onCancelSpy = jasmine.createSpy('onCancel');
      const config: PopoverHandlerConfig = {
        service: mockApiService,
        method: 'retireEmployee',
        onCancel: onCancelSpy
      };

      // Act
      service.handleCancel(config);

      // Assert
      expect(onCancelSpy).toHaveBeenCalled();
    });

    it('should not throw error when onCancel is not provided', () => {
      // Arrange
      const config: PopoverHandlerConfig = {
        service: mockApiService,
        method: 'retireEmployee'
      };

      // Act & Assert
      expect(() => service.handleCancel(config)).not.toThrow();
    });
  });
});
