import { Observable } from 'rxjs';

// ===== INTERFACES =====

export interface TransformConfig {
  commonDataTransform?: (formValue: any, context?: any) => any;
  createDataTransform?: (formValue: any, context?: any) => any;
  updateDataTransform?: (formValue: any, context?: any) => any;
  postDataTransform?: (apiResponse: any, formValue: any, context?: any) => any; // Unified post-API transform
}

export interface ServiceConfig {
  service: any;
  createMethod?: string;
  updateMethod?: string;
  method?: string; // For single operations (popover)
}

export interface ServiceMethodResult {
  success: boolean;
  serviceCall?: any;
  error?: string;
}

// ===== DATA TRANSFORMATION FUNCTIONS =====

/**
 * Unified data transformation for all operations (dialog and popover)
 * For dialog: applies common → create/update layered transformation
 * For popover: applies only commonDataTransform
 * Tree-shakeable: Only bundled when imported
 */
export function preTransformData(
  config: TransformConfig,
  formValue: any,
  isUpdate?: boolean,
  context?: any
): any {
  // Apply commonDataTransform first if it exists
  const commonTransformed = config.commonDataTransform ? config.commonDataTransform(formValue, context) : formValue;

  // For popover operations (isUpdate is undefined), return only common transform
  if (isUpdate === undefined) return commonTransformed;

  // For dialog operations, apply specific transform on top of common transform
  const specificTransformFunction = isUpdate ? config.updateDataTransform : config.createDataTransform;

  return specificTransformFunction
    ? { ...commonTransformed, ...specificTransformFunction(formValue, context) }
    : commonTransformed;
}

/**
 * Unified post-API data transformation following the architecture:
 * commonDataTransform (pre-API) → postDataTransform (post-API)
 * Tree-shakeable: Only bundled when imported
 */
export function postTransformData(
  config: TransformConfig,
  apiResponse: any,
  formValue: any,
  context?: any
): any {
  // Apply commonDataTransform first
  const commonTransformed = config.commonDataTransform ? config.commonDataTransform(formValue, context) : formValue;

  // Then apply postDataTransform on top
  if (config.postDataTransform) {
    const postTransformed = config.postDataTransform(apiResponse, formValue, context);
    return { ...commonTransformed, ...postTransformed };
  }
  return { ...commonTransformed, ...apiResponse };
}

// ===== SERVICE METHOD FUNCTIONS =====

/**
 * Unified service method resolution for all operation types
 * Handles both dialog (create/update) and popover (single method) cases
 * Tree-shakeable: Only bundled when imported
 */
export function resolveServiceMethod(
  config: ServiceConfig,
  isUpdate?: boolean
): ServiceMethodResult {
  let methodName: string | undefined;

  // For popover operations (single method)
  if (config.method) {
    methodName = config.method;
  }
  // For dialog operations (create/update)
  else if (isUpdate !== undefined) {
    methodName = isUpdate ? config.updateMethod : config.createMethod;
  }

  if (!methodName) {
    return { success: false, error: 'Method name not found in config' };
  }

  const serviceMethod = config.service[methodName];
  if (!serviceMethod) {
    return { success: false, error: `Method ${methodName} not found in service` };
  }

  return { success: true, serviceCall: serviceMethod };
}

// ===== MESSAGE GENERATION FUNCTIONS =====

/**
 * Generate success message for dialog operations
 * Tree-shakeable: Only bundled when imported
 */
export function generateDialogSuccessMessage(
  entityLabel?: string,
  isUpdate: boolean = false
): string {
  const action = isUpdate ? 'Cập nhật' : 'Tạo';
  return entityLabel ? `${action} ${entityLabel} thành công` : `${action} thành công`;
}

/**
 * Generate success message for popover operations
 * Tree-shakeable: Only bundled when imported
 */
export function generatePopoverSuccessMessage(entityLabel?: string): string {
  return entityLabel ? `Thao tác ${entityLabel} thành công` : 'Thao tác thành công';
}

/**
 * Generate success message for stepper operations
 * Tree-shakeable: Only bundled when imported
 */
export function generateStepperSuccessMessage(
  entityLabel?: string,
  isUpdate: boolean = false
): string {
  const action = isUpdate ? 'Cập nhật' : 'Tạo';
  return entityLabel ? `${action} ${entityLabel} thành công` : `${action} thành công`;
}





// ===== SERVICE CALL EXECUTION FUNCTIONS =====

export interface ServiceCallResult {
  success: boolean;
  data?: any;
  error?: any;
}

export interface ErrorHandlerService {
  handleInternal(response: any): { hasError: boolean; data?: any };
}

export interface ToastService {
  showSuccess(message: string): void;
}

/**
 * Execute service call with unified error handling and success logic
 * Tree-shakeable: Only bundled when imported
 */
export function executeServiceCall(
  serviceCall: Observable<any>,
  successMessage: string,
  errorHandlerService: ErrorHandlerService,
  toastService: ToastService
): Promise<ServiceCallResult> {
  return new Promise((resolve) => {
    serviceCall.subscribe({
      next: (response) => {
        const { hasError, data } = errorHandlerService.handleInternal(response);
        if (!hasError) {
          toastService.showSuccess(successMessage);
          resolve({ success: true, data });
        } else {
          resolve({ success: false, error: response });
        }
      },
      error: (error) => {
        resolve({ success: false, error });
      }
    });
  });
}
