import { inject } from '@angular/core';
import { EmployeeApiService, EmployeePositionApiService, ProbationApiService } from '@shared/services';
import { StepperConfig, StepperState, StepConfig, StepAction } from '../stepper.interfaces';
import {
  defaultBackAction,
  defaultEditAction,
  defaultSkipAction,
  defaultNextAction,
  defaultEditMode,
  defaultIsDisabled,
  defaultHasPartialData,
  defaultExitConfirmationMessage
} from './defaults';
import {
  employeeInfoFormConfig,
  employeePositionFormConfig,
  employeeProbationFormConfig
} from '@shared/components/edit-create-form';

export const employeeCreationStepperConfig = (onSuccess: () => void, onCancel: () => void): StepperConfig => {
  const employeeApiService = inject(EmployeeApiService);
  const employeePositionApiService = inject(EmployeePositionApiService);
  const probationApiService = inject(ProbationApiService);

  const fieldSelectors = {
    1: (data: any) => `${data.code}, ${data.profile?.fullname || data.fullname}`,
    2: (data: any) => `từ ${data.fromDate ? new Date(data.fromDate).toLocaleDateString('vi-VN') : 'N/A'}`,
    3: (data: any) => `đến ${data.toDate ? new Date(data.toDate).toLocaleDateString('vi-VN') : 'N/A'}`
  };

  const generalConfig = {
    entityType: 'employee-creation',
    width: '600px',
    header: 'Thêm nhân viên mới',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid h-[700px]',
    linear: true,
    onComplete: onSuccess,
    onCancel: onCancel,
    confirmOnClose: true,
    hasPartialData: defaultHasPartialData,
    exitConfirmationMessage: (stepperState: StepperState) => defaultExitConfirmationMessage(stepperState, steps, fieldSelectors)
  };

  const actions: StepAction[] = [
    defaultBackAction,
    defaultEditAction,
    defaultSkipAction,
    defaultNextAction
  ];

  const steps: StepConfig[] = [
    {
      value: 1, label: 'Nhân viên', entityLabel: 'nhân viên',
      formConfig: {
        ...employeeInfoFormConfig(),
        editMode: defaultEditMode, isDisabled: defaultIsDisabled
      },
      handlerConfig: {
        service: employeeApiService,
        createMethod: 'createEmployee',
        updateMethod: 'updateEmployee',
        commonDataTransform: (formValue: any, stepperState: StepperState) => ({
          code: formValue.code?.toUpperCase(),
          fullname: formValue.fullname,
          nickname: formValue.nickname,
          birthday: formValue.birthday ? formValue.birthday : null,
          identification: formValue.identification,
          phone: formValue.phone,
          email: formValue.email,
          note: formValue.note
        }),
        updateDataTransform: (formValue: any, stepperState: StepperState) => ({
          id: stepperState.stepData[1]?.id
        }),
        postDataTransform: (apiResponse: any, formValue: any, stepperState: StepperState) => ({
          id: apiResponse?.id || stepperState.stepData[1]?.id,
          birthday: formValue.birthday ? new Date(formValue.birthday) : null,
          status: apiResponse?.status
        })
      }
    },
    {
      value: 2, label: 'Vị trí', entityLabel: 'vị trí công tác',
      formConfig: {
        ...employeePositionFormConfig(),
        editMode: defaultEditMode, isDisabled: defaultIsDisabled
      },
      handlerConfig: {
        service: employeePositionApiService,
        createMethod: 'assignPositionToEmployee',
        updateMethod: 'updateEmployeePosition',
        commonDataTransform: (formValue: any, stepperState: StepperState) => ({
          employeeId: stepperState.stepData[1]?.id,
          positionId: formValue.positionId,
          departmentId: formValue.departmentId,
          fromDate: formValue.fromDate ? formValue.fromDate : null
          // Note: rootDepartmentId is excluded from API payload as it's only used for form dependencies
        }),
        updateDataTransform: (formValue: any, stepperState: StepperState) => ({
          id: stepperState.stepData[2]?.id
        }),
        postDataTransform: (apiResponse: any, formValue: any, stepperState: StepperState) => ({
          id: apiResponse?.id || stepperState.stepData[2]?.id,
          // Include rootDepartmentId in step data for form population in edit mode
          fromDate: formValue.fromDate ? new Date(formValue.fromDate) : null,
          rootDepartmentId: formValue.rootDepartmentId

        })
      }
    },
    {
      value: 3, label: 'Thử việc', entityLabel: 'thử việc',
      formConfig: {
        ...employeeProbationFormConfig(),
        editMode: defaultEditMode, isDisabled: defaultIsDisabled
      },
      handlerConfig: {
        service: probationApiService,
        createMethod: 'createProbation',
        updateMethod: 'updateProbation',
        commonDataTransform: (formValue: any, stepperState: StepperState) => ({
          employeePositionId: stepperState.stepData[2]?.id,
          toDate: formValue.toDate ? formValue.toDate : null,
          deadline: formValue.deadline ? formValue.deadline : null,
          status: formValue.status,
          isManual: formValue.isManual
        }),
        postDataTransform: (apiResponse: any, formValue: any, stepperState: StepperState) => ({
          id: apiResponse?.id || stepperState.stepData[3]?.id,
          toDate: formValue.toDate ? new Date(formValue.toDate) : null,
          deadline: formValue.deadline ? new Date(formValue.deadline) : null,
        })
      }
    }
  ];

  return {
    ...generalConfig,
    steps,
    actions
  };
};


