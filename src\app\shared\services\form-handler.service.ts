import { Injectable } from '@angular/core';
import { ToastService } from './toast.service';
import { ErrorHandlerService } from './error-handler.service';

// Import existing interfaces from their original locations
import { DialogHandlerConfig } from '@shared/components/edit-create-dialog';
import { PopoverHandlerConfig } from '@shared/components/generic-popover';

// Import utility functions
import {
  preTransformData,
  resolveServiceMethod,
  generateDialogSuccessMessage,
  generateSucce
  executeServiceCall
} from '../utils/handler.utils';

// Internal types for the service
type UnifiedHandlerConfig = DialogHandlerConfig | PopoverHandlerConfig;

interface FormOperationContext {
  config: UnifiedHandlerConfig;
  formValue: any;
  editMode?: boolean; // Only relevant for dialog operations
}

interface FormHandlerResult {
  success: boolean;
  data?: any;
  error?: any;
}

// Type guards to determine configuration type
function isDialogHandlerConfig(config: UnifiedHandlerConfig): config is DialogHandlerConfig {
  return 'createMethod' in config && 'updateMethod' in config;
}





@Injectable({
  providedIn: 'root'
})
export class FormHandlerService {

  constructor(
    private toastService: ToastService,
    private errorHandlerService: ErrorHandlerService
  ) { }

  /**
   * Unified save handler that works with both dialog and popover configurations
   * @param context The operation context containing config, formValue, and editMode
   * @returns Promise that resolves when operation completes
   */
  async handleSave(context: FormOperationContext): Promise<FormHandlerResult> {
    try {
      const { config, formValue, editMode } = context;

      if (!config) {
        throw new Error('Handler configuration is required');
      }

      const result = await this.handleSaveInternal(config, formValue, editMode);
      if (result.success && config.onSuccess) {
        config.onSuccess();
      }

      return result;
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Unified cancel handler that works with both dialog and popover configurations
   * @param config The handler configuration
   */
  handleCancel(config: UnifiedHandlerConfig): void {
    if (config?.onCancel) {
      config.onCancel();
    }
  }

  /**
   * Unified internal save handler for both dialog and popover operations
   */
  private async handleSaveInternal(
    handlerConfig: UnifiedHandlerConfig,
    formValue: any,
    editMode?: boolean
  ): Promise<FormHandlerResult> {
    try {
      // Determine if this is a dialog operation (has editMode) or popover operation
      const isDialogOperation = isDialogHandlerConfig(handlerConfig);
      const isUpdateOperation = isDialogOperation && editMode;

      // Transform data using utility function
      const transformedData = preTransformData(handlerConfig, formValue, isUpdateOperation);

      // Resolve service method using utility function
      const methodResult = resolveServiceMethod(handlerConfig, isUpdateOperation);
      if (!methodResult.success) {
        return { success: false, error: methodResult.error };
      }

      // Execute service method call directly
      const serviceCall = methodResult.serviceCall.call(handlerConfig.service, transformedData);
      const popoverConfig = handlerConfig as PopoverHandlerConfig;
      const successMessage = popoverConfig.successMessage || generateSuccessMessage(handlerConfig.entityLabel, isUpdateOperation);

      return await executeServiceCall(serviceCall, successMessage, this.errorHandlerService, this.toastService);
    } catch (error) {
      return { success: false, error };
    }
  }






}
