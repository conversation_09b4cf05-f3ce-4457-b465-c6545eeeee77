import { Injectable } from '@angular/core';
import { ToastService } from './toast.service';
import { ErrorHandlerService } from './error-handler.service';

// Import existing interfaces from their original locations
import { DialogHandlerConfig } from '@shared/components/edit-create-dialog';
import { PopoverHandlerConfig } from '@shared/components/generic-popover';

// Import utility functions
import {
  preTransformData,
  resolveServiceMethod,
  generateDialogSuccessMessage,
  generatePopoverSuccessMessage,
  executeServiceCall
} from '../utils/handler.utils';

// Internal types for the service
type UnifiedHandlerConfig = DialogHandlerConfig | PopoverHandlerConfig;

interface FormOperationContext {
  config: UnifiedHandlerConfig;
  formValue: any;
  editMode?: boolean; // Only relevant for dialog operations
}

interface FormHandlerResult {
  success: boolean;
  data?: any;
  error?: any;
}

// Type guards to determine configuration type
function isDialogHandlerConfig(config: UnifiedHandlerConfig): config is DialogHandlerConfig {
  return 'createMethod' in config && 'updateMethod' in config;
}

function isPopoverHandlerConfig(config: UnifiedHandlerConfig): config is PopoverHandlerConfig {
  return 'method' in config && !('createMethod' in config);
}



@Injectable({
  providedIn: 'root'
})
export class FormHandlerService {

  constructor(
    private toastService: ToastService,
    private errorHandlerService: ErrorHandlerService
  ) { }

  /**
   * Unified save handler that works with both dialog and popover configurations
   * @param context The operation context containing config, formValue, and editMode
   * @returns Promise that resolves when operation completes
   */
  async handleSave(context: FormOperationContext): Promise<FormHandlerResult> {
    try {
      const { config, formValue, editMode } = context;

      if (!config) {
        throw new Error('Handler configuration is required');
      }

      if (isDialogHandlerConfig(config)) {
        return this.handleDialogSaveInternal(config, formValue, editMode || false);
      } else if (isPopoverHandlerConfig(config)) {
        return this.handlePopoverSaveInternal(config, formValue);
      } else {
        throw new Error('Invalid handler configuration type');
      }
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Unified cancel handler that works with both dialog and popover configurations
   * @param config The handler configuration
   */
  handleCancel(config: UnifiedHandlerConfig): void {
    if (config?.onCancel) {
      config.onCancel();
    }
  }

  /**
   * Handle dialog-specific save operations (create/update)
   */
  private async handleDialogSaveInternal(
    handlerConfig: DialogHandlerConfig,
    formValue: any,
    editMode: boolean
  ): Promise<FormHandlerResult> {
    try {
      // Transform data using utility function
      const transformedData = preTransformData(handlerConfig, formValue, editMode);

      // Resolve service method using utility function
      const methodResult = resolveServiceMethod(handlerConfig, editMode);
      if (!methodResult.success) {
        return { success: false, error: methodResult.error };
      }

      // Execute service method call directly
      const serviceCall = methodResult.serviceCall.call(handlerConfig.service, transformedData);

      // Generate success message using utility function
      const successMessage = generateDialogSuccessMessage(handlerConfig.entityLabel, editMode);

      const result = await executeServiceCall(serviceCall, successMessage, this.errorHandlerService, this.toastService);

      // Handle onSuccess callback in parent logic (like stepper-handler)
      if (result.success && handlerConfig.onSuccess) {
        handlerConfig.onSuccess();
      }

      return result;
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Handle popover-specific save operations (single operation)
   */
  private async handlePopoverSaveInternal(
    handlerConfig: PopoverHandlerConfig,
    formValue: any
  ): Promise<FormHandlerResult> {
    try {
      // Transform data using unified utility function (no isUpdate parameter for popover)
      const transformedData = preTransformData(handlerConfig, formValue);

      // Resolve service method using unified utility function (no isUpdate parameter for popover)
      const methodResult = resolveServiceMethod(handlerConfig);
      if (!methodResult.success) {
        return { success: false, error: methodResult.error };
      }

      // Execute service method call directly
      const serviceCall = methodResult.serviceCall.call(handlerConfig.service, transformedData);

      // Generate success message using utility function
      const successMessage = handlerConfig.successMessage || generatePopoverSuccessMessage(handlerConfig.entityLabel);

      const result = await executeServiceCall(serviceCall, successMessage, this.errorHandlerService, this.toastService);

      // Handle onSuccess callback in parent logic (like stepper-handler)
      if (result.success && handlerConfig.onSuccess) {
        handlerConfig.onSuccess();
      }

      return result;
    } catch (error) {
      return { success: false, error };
    }
  }






}
